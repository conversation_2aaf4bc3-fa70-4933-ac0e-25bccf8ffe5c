"""
Docker配置管理器

专门为Docker环境提供统一的配置管理。
"""

from pathlib import Path
from typing import Dict, Any
from ..manager import ConfigManager
from .base import get_docker_configs
from .logging import get_docker_logging_config
from .ocr import get_docker_ocr_configs


class DockerConfigManager(ConfigManager):
    """
    Docker环境配置管理器
    
    继承自基础配置管理器，专门为Docker环境提供配置。
    """
    
    def __init__(self, base_dir: Path):
        """
        初始化Docker配置管理器
        
        Args:
            base_dir: Django项目的BASE_DIR路径
        """
        super().__init__(base_dir)
        self._docker_configs = None
        self._docker_logging_config = None
        self._docker_ocr_configs = None
    
    @property
    def docker_configs(self) -> Dict[str, Any]:
        """获取Docker基础配置"""
        if self._docker_configs is None:
            self._docker_configs = get_docker_configs(self.base_dir)
        return self._docker_configs
    
    @property
    def docker_logging_config(self) -> Dict[str, Any]:
        """获取Docker日志配置"""
        if self._docker_logging_config is None:
            self._docker_logging_config = get_docker_logging_config()
        return self._docker_logging_config
    
    @property
    def docker_ocr_configs(self) -> Dict[str, Any]:
        """获取Docker OCR配置"""
        if self._docker_ocr_configs is None:
            self._docker_ocr_configs = get_docker_ocr_configs(self.base_dir)
        return self._docker_ocr_configs
    
    def get_all_configs(self) -> Dict[str, Any]:
        """
        获取所有Docker环境配置的合并字典
        
        Returns:
            包含所有Docker配置的字典
        """
        all_configs = {}
        
        # 首先加载基础配置
        all_configs.update(self.django_configs)
        all_configs.update(self.storage_configs)
        
        # 然后加载Docker特定配置（会覆盖基础配置）
        all_configs.update(self.docker_configs)
        
        # 添加特殊配置
        all_configs['OCR_TASK_CONFIGS'] = self.docker_ocr_configs
        all_configs['LOGGING'] = self.docker_logging_config
        
        return all_configs
