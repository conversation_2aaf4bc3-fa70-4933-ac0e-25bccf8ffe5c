anyio==4.9.0
asgiref==3.8.1
astor==0.8.1
certifi==2025.4.26
charset-normalizer==3.4.2
colorama==0.4.6
contourpy==1.3.2
cycler==0.12.1
decorator==5.2.1
Django==5.2.1
django-cors-headers==4.6.0
djangorestframework==3.15.1
filelock==3.18.0
# fonttools==4.57.0  # 移动到PaddleOCR依赖部分
fsspec==2025.3.2
h11==0.16.0
httpcore==1.0.9
httpx==0.28.1
idna==3.10
Jinja2==3.1.6
kiwisolver==1.4.8
MarkupSafe==3.0.2
matplotlib==3.10.1
mpmath==1.3.0
networkx==3.4.2
numpy>=1.23.0,<2.0.0     # ultralytics 8.3.0 版本要求 numpy<2.0.0 且 >=1.23.0
opencv-python==*********
opt-einsum==3.3.0
packaging==25.0
paddlepaddle==3.0.0
pandas==2.2.3
pillow==11.2.1
protobuf==6.30.2
psutil==7.0.0
py-cpuinfo==9.0.0
pyparsing==3.2.3
PyWavelets==1.7.0
python-dateutil==2.9.0.post0
timm==1.0.12
pytz==2025.2
PyYAML==6.0.2
# 几何处理和图像处理相关
shapely==2.0.6
# ONNX推理引擎
onnxruntime==1.20.1
# 其他可能缺少的依赖
opencv-contrib-python==*********
scikit-image==0.24.0
imageio==2.37.0
# PaddleOCR相关依赖
paddleocr==2.10.0
pyclipper==1.3.0.post5
lmdb==1.5.1
# PaddleOCR额外依赖
albucore==0.0.21
albumentations==1.4.23
beautifulsoup4==4.12.3
cython==3.0.11
fire==0.7.0
fonttools==4.57.0
python-docx==1.1.2
rapidfuzz==3.10.1
# 数据处理相关
h5py==3.12.1
# 可视化相关
matplotlib-inline==0.1.7
# 图像增强库
imgaug==0.4.0
requests==2.32.3
scipy==1.15.2
seaborn==0.13.2
setuptools==80.3.1
six==1.17.0
sniffio==1.3.1
sqlparse==0.5.3
sympy==1.14.0
# torch==2.7.0  # 在Dockerfile中单独安装
# torchvision==0.22.0  # 在Dockerfile中单独安装
typing_extensions==4.13.2
tzdata==2025.2
# 使用公司修改后的 ultralytics 版本，源于 http://************:3000/Project_Deep_Learning/proj_ai_roi_det.git
# 本地安装以避免单通道模型推理时的通道不匹配问题。
# 注释掉本地安装，在Dockerfile中单独处理
# -e ./libs/proj_ai_roi_det
ultralytics-thop==2.0.14
urllib3==2.4.0