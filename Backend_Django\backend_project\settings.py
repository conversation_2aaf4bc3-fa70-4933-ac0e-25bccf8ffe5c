"""
Django settings for backend_project project.

Generated by 'django-admin startproject' using Django 5.2.1.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

from pathlib import Path
import os

# 导入配置管理器
from .config.manager import ConfigManager

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# 初始化配置管理器
config_manager = ConfigManager(BASE_DIR)

# 获取所有配置
_all_configs = config_manager.get_all_configs()

# 将配置应用到当前模块的全局变量中
globals().update(_all_configs)
