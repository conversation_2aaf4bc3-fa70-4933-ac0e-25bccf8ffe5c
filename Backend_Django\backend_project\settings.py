"""
Django settings for backend_project project.

Generated by 'django-admin startproject' using Django 5.2.1.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

from pathlib import Path
import os

# 导入配置模块
from .config.ocr_configs import get_ocr_task_configs
from .config.storage_configs import get_storage_configs

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-q!qvev@pe__qs170saztzqy%^6msnq11&@yt(_@-!%(mc=($h-"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['*************', '*'] # 允许开发机IP和所有主机访问


# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "corsheaders", # 添加 corsheaders
    "vision_app",
]

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware", # 添加 corsheaders 中间件
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "backend_project.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "backend_project.wsgi.application"


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": BASE_DIR / "db" / "db.sqlite3",
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

# 加载存储配置
_storage_configs = get_storage_configs(BASE_DIR)

STATIC_URL = _storage_configs['STATIC_URL']
MEDIA_URL = _storage_configs['MEDIA_URL']
MEDIA_ROOT = _storage_configs['MEDIA_ROOT']
SYSTEM_MODELS_ROOT = _storage_configs['SYSTEM_MODELS_ROOT']
EXAMPLE_IMAGES_ROOT = _storage_configs['EXAMPLE_IMAGES_ROOT']

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# CORS settings
# CORS_ALLOW_ALL_ORIGINS = True # 禁用全开放，改用白名单
CORS_ALLOWED_ORIGINS = [
    "http://localhost:5173",
    "http://127.0.0.1:5173",
    "http://*************:5173",
]
CSRF_TRUSTED_ORIGINS = [
    "http://localhost:5173",
    "http://127.0.0.1:5173",
    "http://*************:5173",
]
CORS_ALLOW_CREDENTIALS = True # 允许发送 cookies 和会话信息

# 会话配置
SESSION_COOKIE_AGE = 86400  # 会话有效期：24小时（秒）
SESSION_SAVE_EVERY_REQUEST = True  # 每次请求都保存会话
SESSION_EXPIRE_AT_BROWSER_CLOSE = False  # 浏览器关闭时不过期会话
SESSION_COOKIE_SAMESITE = "Lax"  # 本地开发推荐Lax，跨域如需可用None+https
SESSION_COOKIE_SECURE = False     # 本地开发用False，生产https用True

# OCR Task Specific Configurations
# 加载OCR配置
OCR_TASK_CONFIGS = get_ocr_task_configs(BASE_DIR)
