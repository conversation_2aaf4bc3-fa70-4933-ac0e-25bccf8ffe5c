"""
Docker环境基础配置

包含Docker容器环境的基础配置参数。
"""

from pathlib import Path
from typing import Dict, Any


def get_docker_configs(base_dir: Path) -> Dict[str, Any]:
    """
    获取Docker环境基础配置
    
    Args:
        base_dir: Django项目的BASE_DIR路径
        
    Returns:
        Docker环境配置字典
    """
    return {
        # Docker环境基础配置
        'DEBUG': True,  # Docker环境可以开启以便排查问题
        'ALLOWED_HOSTS': ['*'],  # Docker环境需要允许所有主机
        
        # Docker环境的数据库配置 - 使用挂载的Volume路径
        'DATABASES': {
            'default': {
                'ENGINE': 'django.db.backends.sqlite3',
                'NAME': '/app/db/db.sqlite3',  # 使用绝对路径指向挂载的Volume
                'OPTIONS': {
                    'timeout': 30,  # 增加超时时间
                }
            }
        },
        
        # Docker环境的存储配置 - 使用挂载的Volume路径
        'MEDIA_ROOT': '/app/models/custom_models',
        'SYSTEM_MODELS_ROOT': '/app/models/system_models',
        'EXAMPLE_IMAGES_ROOT': '/app/models/example_images',
        
        # 静态文件设置
        'STATIC_ROOT': '/app/staticfiles',
        'STATIC_URL': '/static/',
        
        # Docker环境的CORS配置 - 允许所有源
        'CORS_ALLOW_ALL_ORIGINS': True,
        'CORS_ALLOW_CREDENTIALS': True,
        'CORS_ALLOWED_ORIGINS': [
            "http://localhost:8080",
            "http://127.0.0.1:8080",
            "http://*************:8080",
            "http://*************:8080",
        ],
        'CORS_ALLOW_HEADERS': [
            'accept',
            'accept-encoding',
            'authorization',
            'content-type',
            'dnt',
            'origin',
            'user-agent',
            'x-csrftoken',
            'x-requested-with',
        ],
        'CORS_ALLOW_METHODS': [
            'DELETE',
            'GET',
            'OPTIONS',
            'PATCH',
            'POST',
            'PUT',
        ],
        
        # Docker环境的安全设置 - 开发环境可以放宽
        'SECURE_BROWSER_XSS_FILTER': False,
        'SECURE_CONTENT_TYPE_NOSNIFF': False,
        'X_FRAME_OPTIONS': 'SAMEORIGIN',
        
        # 文件上传设置
        'FILE_UPLOAD_MAX_MEMORY_SIZE': 200 * 1024 * 1024,  # 200MB
        'DATA_UPLOAD_MAX_MEMORY_SIZE': 200 * 1024 * 1024,  # 200MB
        
        # Docker环境的会话设置
        'SESSION_COOKIE_AGE': 86400 * 7,  # 7天
        'SESSION_SAVE_EVERY_REQUEST': False,
        
        # 时区设置
        'USE_TZ': True,
        'TIME_ZONE': 'Asia/Shanghai',
        
        # 国际化设置
        'LANGUAGE_CODE': 'zh-hans',
        'USE_I18N': True,
        'USE_L10N': True,
        
        # 缓存设置 - 使用内存缓存
        'CACHES': {
            'default': {
                'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
                'LOCATION': 'docker-cache',
                'TIMEOUT': 300,
                'OPTIONS': {
                    'MAX_ENTRIES': 1000
                }
            }
        },
    }
