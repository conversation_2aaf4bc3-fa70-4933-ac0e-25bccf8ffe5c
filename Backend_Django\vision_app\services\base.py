"""
基础服务类

提供所有服务类的基础功能和通用方法。
"""

import logging
from typing import Any, Dict, Optional
from django.conf import settings


class BaseService:
    """
    基础服务类
    
    所有服务类的基类，提供通用的功能和方法。
    """
    
    def __init__(self):
        """初始化基础服务"""
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def log_info(self, message: str, **kwargs) -> None:
        """记录信息日志"""
        self.logger.info(message, **kwargs)
    
    def log_warning(self, message: str, **kwargs) -> None:
        """记录警告日志"""
        self.logger.warning(message, **kwargs)
    
    def log_error(self, message: str, **kwargs) -> None:
        """记录错误日志"""
        self.logger.error(message, **kwargs)
    
    def log_debug(self, message: str, **kwargs) -> None:
        """记录调试日志"""
        self.logger.debug(message, **kwargs)
    
    def get_setting(self, setting_name: str, default: Any = None) -> Any:
        """
        获取Django设置
        
        Args:
            setting_name: 设置名称
            default: 默认值
            
        Returns:
            设置值
        """
        return getattr(settings, setting_name, default)
    
    def create_error_response(self, message: str, details: Optional[Dict] = None) -> Dict[str, Any]:
        """
        创建标准错误响应
        
        Args:
            message: 错误消息
            details: 错误详情
            
        Returns:
            错误响应字典
        """
        response = {
            'success': False,
            'error': message
        }
        if details:
            response['details'] = details
        return response
    
    def create_success_response(self, data: Any = None, message: str = None) -> Dict[str, Any]:
        """
        创建标准成功响应
        
        Args:
            data: 响应数据
            message: 成功消息
            
        Returns:
            成功响应字典
        """
        response = {
            'success': True
        }
        if data is not None:
            response['data'] = data
        if message:
            response['message'] = message
        return response
